# Test du Masque Astral

## Fonctionnalités ajoutées :

### 1. Nouveau masque à gaz astral
- **Nom** : "Astral Gas Mask"
- **Template** : "astralmask"
- **Image d'inventaire** : GFX\items\INVastralmask.jpg (placeholder créé)

### 2. Variables globales ajoutées
- `WearingAstralMask%` : Indique si le joueur porte le masque astral
- `AstralMode%` : Indique si le mode astral est actif
- `PossessedNPC.NPCs` : Référence vers le NPC actuellement possédé

### 3. Fonctionnalités du masque astral

#### Mode astral
- Quand le masque est porté, le joueur entre dans le plan astral
- Les NPCs ne peuvent plus voir le joueur (modification de `MeNPCSeesPlayer`)
- Affichage d'un overlay de masque à gaz comme les autres masques

#### Possession de NPCs
- **Touche E** : Posséder/libérer un NPC proche (distance max : 3.0 unités)
- **Contrôles du NPC possédé** :
  - WASD : Déplacement du NPC
  - Le NPC suit les commandes du joueur
  - Animation de marche automatique
  - L'IA normale du NPC est désactivée pendant la possession

#### Interface utilisateur
- Indicateur "ASTRAL MODE ACTIVE" en haut à droite
- Affichage du NPC possédé : "Possessing: [Nom du NPC]"
- Instructions : "Press E to release control" ou "Press E near NPC to possess"

### 4. Restrictions
- Ne peut pas posséder certains NPCs (Apache, Tentacle)
- Le masque astral ne peut pas être porté avec d'autres masques ou combinaisons
- Quand le masque est retiré, la possession se termine automatiquement

## Comment tester :

1. Lancer le jeu
2. Utiliser la console pour spawner le masque : `spawn item astralmask`
3. Équiper le masque (clic droit dans l'inventaire)
4. Observer l'indicateur "ASTRAL MODE ACTIVE"
5. Approcher d'un NPC et appuyer sur E pour le posséder
6. Utiliser WASD pour contrôler le NPC possédé
7. Appuyer sur E pour libérer le contrôle

## Fichiers modifiés :

1. **Main.bb** :
   - Ajout des variables globales
   - Contrôles de possession (touche E)
   - Fonction `UpdatePossessedNPC()`
   - Indicateurs visuels dans `DrawGUI()`
   - Gestion de l'overlay du masque

2. **Items.bb** :
   - Ajout du template "astralmask"
   - Gestion dans `RemoveItem()` et `DropItem()`

3. **NPCs.bb** :
   - Modification de `MeNPCSeesPlayer()` pour l'invisibilité astrale

4. **GFX/items/INVastralmask.jpg** :
   - Image d'inventaire (placeholder créé)
